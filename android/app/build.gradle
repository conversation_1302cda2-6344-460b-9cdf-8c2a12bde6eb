def localProperties = new Properties()
def localPropertiesFile = rootProject.file('local.properties')
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader('UTF-8') { reader ->
        localProperties.load(reader)
    }
}
 def keystoreProperties = new Properties()	
   def keystorePropertiesFile = rootProject.file('key.properties')	
   if (keystorePropertiesFile.exists()) {	
       keystoreProperties.load(new FileInputStream(keystorePropertiesFile))	
   }


def flutterVersionCode = localProperties.getProperty('flutter.versionCode')
if (flutterVersionCode == null) {
    flutterVersionCode = '1'
}

def flutterVersionName = localProperties.getProperty('flutter.versionName')
if (flutterVersionName == null) {
    flutterVersionName = '1.0'
}

plugins {
    id "com.android.application"
    id "kotlin-android"
    // START: FlutterFire Configuration
    id "com.google.gms.google-services"
    // END: FlutterFire Configuration
    id "dev.flutter.flutter-gradle-plugin"
}

android {
    compileSdkVersion 33

    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
    }

    defaultConfig {
        // TODO: Specify your own unique Application ID (https://developer.android.com/studio/build/application-id.html).
        applicationId "ai.sunridercrew.app"
        minSdkVersion 21
        targetSdkVersion 33
        versionCode flutterVersionCode.toInteger()
        versionName flutterVersionName
    }
     signingConfigs {	
       release {	
           keyAlias keystoreProperties['keyAlias']	
           keyPassword keystoreProperties['keyPassword']	
           storeFile keystoreProperties['storeFile'] ? file(keystoreProperties['storeFile']) : null	
           storePassword keystoreProperties['storePassword']	
       }	
    }

    buildTypes {
        release {
            // TODO: Add your own signing config for the release build.
            // Signing with the debug keys for now, so `flutter run --release` works.
            signingConfig signingConfigs.release
        }
    }

    dependencies {
        implementation 'androidx.browser:browser:1.0.0'
    }
}

flutter {
    source '../..'
}

dependencies {
    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk7:$kotlin_version"
}
