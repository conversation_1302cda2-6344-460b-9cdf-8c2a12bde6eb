{"configVersion": 2, "packages": [{"name": "_fe_analyzer_shared", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/_fe_analyzer_shared-67.0.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "_flutterfire_internals", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/_flutterfire_internals-1.3.16", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "adaptive_number", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/adaptive_number-1.0.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "analyzer", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/analyzer-6.4.1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "archive", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/archive-3.4.9", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "args", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/args-2.4.2", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "async", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/async-2.12.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "auto_size_text", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/auto_size_text-3.0.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "bloc", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/bloc-8.1.4", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "bloc_test", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/bloc_test-9.1.7", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "boolean_selector", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/boolean_selector-2.1.2", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "build", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/build-2.4.1", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "build_config", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/build_config-1.1.1", "packageUri": "lib/", "languageVersion": "2.14"}, {"name": "build_daemon", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/build_daemon-4.0.1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "build_resolvers", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/build_resolvers-2.4.2", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "build_runner", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/build_runner-2.4.9", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "build_runner_core", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/build_runner_core-7.3.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "built_collection", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/built_collection-5.1.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "built_value", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/built_value-8.9.2", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "characters", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "charcode", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/charcode-1.3.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "checked_yaml", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/checked_yaml-2.0.3", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "cli_util", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/cli_util-0.4.0", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "clock", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/clock-1.1.2", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "code_builder", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/code_builder-4.10.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "collection", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "convert", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/convert-3.1.1", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "coverage", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/coverage-1.8.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "cross_file", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/cross_file-0.3.3+7", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "crypto", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.3", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "csslib", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/csslib-0.17.3", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "cupertino_icons", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/cupertino_icons-1.0.6", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "dart_jsonwebtoken", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/dart_jsonwebtoken-2.12.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "dart_style", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/dart_style-2.3.6", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "dbus", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.10", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "device_info_plus", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-9.1.1", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "device_info_plus_platform_interface", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus_platform_interface-7.0.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "diff_match_patch", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/diff_match_patch-0.4.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "dio", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.3.4", "packageUri": "lib/", "languageVersion": "2.15"}, {"name": "ed25519_edwards", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/ed25519_edwards-0.3.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "equatable", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/equatable-2.0.5", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "fading_edge_scrollview", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/fading_edge_scrollview-4.0.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "fake_async", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/fake_async-1.3.2", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "ffi", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/ffi-2.1.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "file", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "file_selector_linux", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_linux-0.9.2+1", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "file_selector_macos", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_macos-0.9.3+3", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "file_selector_platform_interface", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_platform_interface-2.6.1", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "file_selector_windows", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_windows-0.9.3+1", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "firebase_core", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core-2.24.2", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "firebase_core_platform_interface", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core_platform_interface-5.0.0", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "firebase_core_web", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core_web-2.10.0", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "firebase_messaging", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_messaging-14.7.10", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "firebase_messaging_platform_interface", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_messaging_platform_interface-4.5.18", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "firebase_messaging_web", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_messaging_web-3.5.18", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "fixnum", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/fixnum-1.1.0", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "fluro", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/fluro-2.0.5", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "flutter", "rootUri": "file:///Users/<USER>/development/flutter/packages/flutter", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "flutter_app_badger", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_app_badger-1.5.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "flutter_bloc", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_bloc-8.1.6", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "flutter_form_builder", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_form_builder-9.7.0", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "flutter_html", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_html-3.0.0-alpha.5", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "flutter_inappwebview", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview-5.8.0", "packageUri": "lib/", "languageVersion": "2.14"}, {"name": "flutter_launcher_icons", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_launcher_icons-0.13.1", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "flutter_local_notifications", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications-16.3.0", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "flutter_local_notifications_linux", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications_linux-4.0.0+1", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "flutter_local_notifications_platform_interface", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications_platform_interface-7.0.0+1", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "flutter_localizations", "rootUri": "file:///Users/<USER>/development/flutter/packages/flutter_localizations", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "flutter_plugin_android_lifecycle", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_plugin_android_lifecycle-2.0.17", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "flutter_secure_storage", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-9.0.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "flutter_secure_storage_linux", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_linux-1.2.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "flutter_secure_storage_macos", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_macos-3.0.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "flutter_secure_storage_platform_interface", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_platform_interface-1.0.2", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "flutter_secure_storage_web", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_web-1.1.2", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "flutter_secure_storage_windows", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_windows-3.0.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "flutter_slidable", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_slidable-3.1.1", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "flutter_speed_dial", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_speed_dial-7.0.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "flutter_staggered_grid_view", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_staggered_grid_view-0.7.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "flutter_svg", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_svg-2.0.9", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "flutter_test", "rootUri": "file:///Users/<USER>/development/flutter/packages/flutter_test", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "flutter_web_plugins", "rootUri": "file:///Users/<USER>/development/flutter/packages/flutter_web_plugins", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "form_builder_validators", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/form_builder_validators-11.1.2", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "frontend_server_client", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/frontend_server_client-3.2.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "geolocator", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/geolocator-10.1.0", "packageUri": "lib/", "languageVersion": "2.15"}, {"name": "geolocator_android", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_android-4.4.0", "packageUri": "lib/", "languageVersion": "2.15"}, {"name": "geolocator_apple", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_apple-2.3.2", "packageUri": "lib/", "languageVersion": "2.15"}, {"name": "geolocator_platform_interface", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_platform_interface-4.2.0", "packageUri": "lib/", "languageVersion": "2.15"}, {"name": "geolocator_web", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_web-2.2.0", "packageUri": "lib/", "languageVersion": "2.15"}, {"name": "geolocator_windows", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_windows-0.2.2", "packageUri": "lib/", "languageVersion": "2.15"}, {"name": "get_it", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/get_it-7.7.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "glob", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/glob-2.1.2", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "graphs", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/graphs-2.3.1", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "hive", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/hive-2.2.3", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "hive_flutter", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/hive_flutter-1.1.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "hive_generator", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/hive_generator-2.0.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "html", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/html-0.15.4", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "http", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/http-1.1.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "http_multi_server", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/http_multi_server-3.2.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "http_parser", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.0.2", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "image", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/image-4.1.3", "packageUri": "lib/", "languageVersion": "2.15"}, {"name": "image_picker", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker-1.0.4", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "image_picker_android", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_android-0.8.8+2", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "image_picker_for_web", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_for_web-3.0.1", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "image_picker_ios", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_ios-0.8.8+4", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "image_picker_linux", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_linux-0.2.1+1", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "image_picker_macos", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_macos-0.2.1+1", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "image_picker_platform_interface", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.9.1", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "image_picker_windows", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_windows-0.2.1+1", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "infinite_scroll_pagination", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/infinite_scroll_pagination-4.0.0", "packageUri": "lib/", "languageVersion": "2.14"}, {"name": "intl", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.19.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "io", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/io-1.0.4", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "jovial_misc", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/jovial_misc-0.8.5", "packageUri": "lib/", "languageVersion": "2.14"}, {"name": "jovial_svg", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/jovial_svg-1.1.19", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "js", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/js-0.6.7", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "json_annotation", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/json_annotation-4.8.1", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "jwt_decoder", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/jwt_decoder-2.0.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "leak_tracker", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker-10.0.8", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "leak_tracker_flutter_testing", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_flutter_testing-3.0.9", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "leak_tracker_testing", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_testing-3.0.1", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "logger", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.0.2+1", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "logging", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/logging-1.2.0", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "matcher", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/matcher-0.12.17", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "material_color_utilities", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "material_design_icons_flutter", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/material_design_icons_flutter-7.0.7296", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "meta", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/meta-1.16.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "mime", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/mime-1.0.4", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "mocktail", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/mocktail-1.0.4", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "nested", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/nested-1.0.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "node_preamble", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/node_preamble-2.0.2", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "numerus", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/numerus-1.1.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "package_config", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/package_config-2.1.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "package_info_plus", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus-5.0.1", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "package_info_plus_platform_interface", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus_platform_interface-2.0.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "path", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "path_parsing", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_parsing-1.0.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "path_provider", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider-2.1.1", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "path_provider_android", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_android-2.2.1", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "path_provider_foundation", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.3.1", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "path_provider_linux", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "path_provider_platform_interface", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_platform_interface-2.1.1", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "path_provider_windows", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.2.1", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "petitparser", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "platform", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/platform-3.1.3", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "plugin_platform_interface", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/plugin_platform_interface-2.1.7", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "pointycastle", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.7.3", "packageUri": "lib/", "languageVersion": "2.14"}, {"name": "pool", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/pool-1.5.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "preload_page_view", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/preload_page_view-0.2.0", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "provider", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/provider-6.1.2", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "pub_semver", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/pub_semver-2.1.4", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "pubspec_parse", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/pubspec_parse-1.3.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "qr_code_scanner", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/qr_code_scanner-1.0.1", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "shelf", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/shelf-1.4.1", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "shelf_packages_handler", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/shelf_packages_handler-3.0.2", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "shelf_static", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/shelf_static-1.1.2", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "shelf_web_socket", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/shelf_web_socket-1.0.4", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "sky_engine", "rootUri": "file:///Users/<USER>/development/flutter/bin/cache/pkg/sky_engine", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "sliver_tools", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/sliver_tools-0.2.12", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "source_gen", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/source_gen-1.5.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "source_helper", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/source_helper-1.3.4", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "source_map_stack_trace", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/source_map_stack_trace-2.1.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "source_maps", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/source_maps-0.10.12", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "source_span", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "sprintf", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/sprintf-7.0.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "stack_trace", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/stack_trace-1.12.1", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "stream_channel", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/stream_channel-2.1.4", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "stream_transform", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/stream_transform-2.1.0", "packageUri": "lib/", "languageVersion": "2.14"}, {"name": "string_scanner", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "term_glyph", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.2", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "test", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/test-1.25.15", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "test_api", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/test_api-0.7.4", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "test_core", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/test_core-0.6.8", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "thingsboard_client", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/thingsboard_client-1.2.1", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "timeago", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "timezone", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/timezone-0.9.2", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "timing", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/timing-1.0.1", "packageUri": "lib/", "languageVersion": "2.14"}, {"name": "typed_data", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/typed_data-1.3.2", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "uni_links", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/uni_links-0.5.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "uni_links_platform_interface", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/uni_links_platform_interface-1.0.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "uni_links_web", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/uni_links_web-0.1.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "universal_html", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/universal_html-2.2.4", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "universal_io", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/universal_io-2.2.2", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "universal_platform", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/universal_platform-1.0.0+1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "url_launcher", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher-6.2.1", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "url_launcher_android", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_android-6.2.0", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "url_launcher_ios", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_ios-6.2.1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "url_launcher_linux", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_linux-3.1.0", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "url_launcher_macos", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_macos-3.1.0", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "url_launcher_platform_interface", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_platform_interface-2.2.0", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "url_launcher_web", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_web-2.2.1", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "url_launcher_windows", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_windows-3.1.0", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "uuid", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.2.1", "packageUri": "lib/", "languageVersion": "2.14"}, {"name": "vector_graphics", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/vector_graphics-1.1.9+1", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "vector_graphics_codec", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/vector_graphics_codec-1.1.9+1", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "vector_graphics_compiler", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/vector_graphics_compiler-1.1.9+1", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "vector_math", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4", "packageUri": "lib/", "languageVersion": "2.14"}, {"name": "vm_service", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/vm_service-14.3.1", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "watcher", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/watcher-1.1.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "web", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/web-0.3.0", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "web_socket_channel", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/web_socket_channel-2.4.0", "packageUri": "lib/", "languageVersion": "2.15"}, {"name": "webkit_inspection_protocol", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/webkit_inspection_protocol-1.2.1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "win32", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.1.0", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "win32_registry", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/win32_registry-1.1.2", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "xdg_directories", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/xdg_directories-1.0.3", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "xml", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.4.2", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "yaml", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/yaml-3.1.2", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "thingsboard_app", "rootUri": "../", "packageUri": "lib/", "languageVersion": "3.2"}], "generated": "2025-06-19T11:44:56.451573Z", "generator": "pub", "generatorVersion": "3.7.2", "flutterRoot": "file:///Users/<USER>/development/flutter", "flutterVersion": "3.29.2", "pubCache": "file:///Users/<USER>/.pub-cache"}