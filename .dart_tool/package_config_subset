_fe_analyzer_shared
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/_fe_analyzer_shared-67.0.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/_fe_analyzer_shared-67.0.0/lib/
_flutterfire_internals
2.18
file:///Users/<USER>/.pub-cache/hosted/pub.dev/_flutterfire_internals-1.3.16/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/_flutterfire_internals-1.3.16/lib/
adaptive_number
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/adaptive_number-1.0.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/adaptive_number-1.0.0/lib/
analyzer
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/analyzer-6.4.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/analyzer-6.4.1/lib/
archive
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/archive-3.4.9/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/archive-3.4.9/lib/
args
2.19
file:///Users/<USER>/.pub-cache/hosted/pub.dev/args-2.4.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/args-2.4.2/lib/
async
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/async-2.12.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/async-2.12.0/lib/
auto_size_text
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/auto_size_text-3.0.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/auto_size_text-3.0.0/lib/
bloc
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/bloc-8.1.4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/bloc-8.1.4/lib/
bloc_test
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/bloc_test-9.1.7/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/bloc_test-9.1.7/lib/
boolean_selector
3.1
file:///Users/<USER>/.pub-cache/hosted/pub.dev/boolean_selector-2.1.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/boolean_selector-2.1.2/lib/
build
2.19
file:///Users/<USER>/.pub-cache/hosted/pub.dev/build-2.4.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/build-2.4.1/lib/
build_config
2.14
file:///Users/<USER>/.pub-cache/hosted/pub.dev/build_config-1.1.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/build_config-1.1.1/lib/
build_daemon
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/build_daemon-4.0.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/build_daemon-4.0.1/lib/
build_resolvers
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/build_resolvers-2.4.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/build_resolvers-2.4.2/lib/
build_runner
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/build_runner-2.4.9/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/build_runner-2.4.9/lib/
build_runner_core
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/build_runner_core-7.3.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/build_runner_core-7.3.0/lib/
built_collection
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/built_collection-5.1.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/built_collection-5.1.1/lib/
built_value
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/built_value-8.9.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/built_value-8.9.2/lib/
characters
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0/lib/
charcode
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/charcode-1.3.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/charcode-1.3.1/lib/
checked_yaml
2.19
file:///Users/<USER>/.pub-cache/hosted/pub.dev/checked_yaml-2.0.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/checked_yaml-2.0.3/lib/
cli_util
2.19
file:///Users/<USER>/.pub-cache/hosted/pub.dev/cli_util-0.4.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/cli_util-0.4.0/lib/
clock
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/clock-1.1.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/clock-1.1.2/lib/
code_builder
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/code_builder-4.10.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/code_builder-4.10.0/lib/
collection
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/
convert
2.18
file:///Users/<USER>/.pub-cache/hosted/pub.dev/convert-3.1.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/convert-3.1.1/lib/
coverage
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/coverage-1.8.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/coverage-1.8.0/lib/
cross_file
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/cross_file-0.3.3+7/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/cross_file-0.3.3+7/lib/
crypto
2.19
file:///Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.3/lib/
csslib
2.19
file:///Users/<USER>/.pub-cache/hosted/pub.dev/csslib-0.17.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/csslib-0.17.3/lib/
cupertino_icons
2.19
file:///Users/<USER>/.pub-cache/hosted/pub.dev/cupertino_icons-1.0.6/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/cupertino_icons-1.0.6/lib/
dart_jsonwebtoken
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/dart_jsonwebtoken-2.12.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/dart_jsonwebtoken-2.12.1/lib/
dart_style
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/dart_style-2.3.6/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/dart_style-2.3.6/lib/
dbus
2.17
file:///Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.10/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.10/lib/
device_info_plus
2.18
file:///Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-9.1.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-9.1.1/lib/
device_info_plus_platform_interface
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus_platform_interface-7.0.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus_platform_interface-7.0.0/lib/
diff_match_patch
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/diff_match_patch-0.4.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/diff_match_patch-0.4.1/lib/
dio
2.15
file:///Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.3.4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.3.4/lib/
ed25519_edwards
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/ed25519_edwards-0.3.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/ed25519_edwards-0.3.1/lib/
equatable
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/equatable-2.0.5/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/equatable-2.0.5/lib/
fading_edge_scrollview
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/fading_edge_scrollview-4.0.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/fading_edge_scrollview-4.0.0/lib/
fake_async
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/fake_async-1.3.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/fake_async-1.3.2/lib/
ffi
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/ffi-2.1.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/ffi-2.1.0/lib/
file
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.0/lib/
file_selector_linux
2.19
file:///Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_linux-0.9.2+1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_linux-0.9.2+1/lib/
file_selector_macos
2.19
file:///Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_macos-0.9.3+3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_macos-0.9.3+3/lib/
file_selector_platform_interface
2.19
file:///Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_platform_interface-2.6.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_platform_interface-2.6.1/lib/
file_selector_windows
2.19
file:///Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_windows-0.9.3+1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_windows-0.9.3+1/lib/
firebase_core
2.18
file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core-2.24.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core-2.24.2/lib/
firebase_core_platform_interface
2.18
file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core_platform_interface-5.0.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core_platform_interface-5.0.0/lib/
firebase_core_web
2.18
file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core_web-2.10.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core_web-2.10.0/lib/
firebase_messaging
2.18
file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_messaging-14.7.10/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_messaging-14.7.10/lib/
firebase_messaging_platform_interface
2.18
file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_messaging_platform_interface-4.5.18/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_messaging_platform_interface-4.5.18/lib/
firebase_messaging_web
2.18
file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_messaging_web-3.5.18/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/firebase_messaging_web-3.5.18/lib/
fixnum
2.19
file:///Users/<USER>/.pub-cache/hosted/pub.dev/fixnum-1.1.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/fixnum-1.1.0/lib/
fluro
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/fluro-2.0.5/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/fluro-2.0.5/lib/
flutter_app_badger
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_app_badger-1.5.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_app_badger-1.5.0/lib/
flutter_bloc
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_bloc-8.1.6/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_bloc-8.1.6/lib/
flutter_form_builder
3.6
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_form_builder-9.7.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_form_builder-9.7.0/lib/
flutter_html
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_html-3.0.0-alpha.5/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_html-3.0.0-alpha.5/lib/
flutter_inappwebview
2.14
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview-5.8.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview-5.8.0/lib/
flutter_launcher_icons
2.18
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_launcher_icons-0.13.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_launcher_icons-0.13.1/lib/
flutter_local_notifications
2.17
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications-16.3.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications-16.3.0/lib/
flutter_local_notifications_linux
2.17
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications_linux-4.0.0+1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications_linux-4.0.0+1/lib/
flutter_local_notifications_platform_interface
2.17
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications_platform_interface-7.0.0+1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications_platform_interface-7.0.0+1/lib/
flutter_plugin_android_lifecycle
2.19
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_plugin_android_lifecycle-2.0.17/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_plugin_android_lifecycle-2.0.17/lib/
flutter_secure_storage
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-9.0.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-9.0.0/lib/
flutter_secure_storage_linux
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_linux-1.2.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_linux-1.2.0/lib/
flutter_secure_storage_macos
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_macos-3.0.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_macos-3.0.1/lib/
flutter_secure_storage_platform_interface
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_platform_interface-1.0.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_platform_interface-1.0.2/lib/
flutter_secure_storage_web
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_web-1.1.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_web-1.1.2/lib/
flutter_secure_storage_windows
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_windows-3.0.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_windows-3.0.0/lib/
flutter_slidable
2.17
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_slidable-3.1.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_slidable-3.1.1/lib/
flutter_speed_dial
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_speed_dial-7.0.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_speed_dial-7.0.0/lib/
flutter_staggered_grid_view
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_staggered_grid_view-0.7.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_staggered_grid_view-0.7.0/lib/
flutter_svg
2.19
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_svg-2.0.9/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_svg-2.0.9/lib/
form_builder_validators
3.6
file:///Users/<USER>/.pub-cache/hosted/pub.dev/form_builder_validators-11.1.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/form_builder_validators-11.1.2/lib/
frontend_server_client
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/frontend_server_client-3.2.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/frontend_server_client-3.2.0/lib/
geolocator
2.15
file:///Users/<USER>/.pub-cache/hosted/pub.dev/geolocator-10.1.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/geolocator-10.1.0/lib/
geolocator_android
2.15
file:///Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_android-4.4.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_android-4.4.0/lib/
geolocator_apple
2.15
file:///Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_apple-2.3.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_apple-2.3.2/lib/
geolocator_platform_interface
2.15
file:///Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_platform_interface-4.2.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_platform_interface-4.2.0/lib/
geolocator_web
2.15
file:///Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_web-2.2.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_web-2.2.0/lib/
geolocator_windows
2.15
file:///Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_windows-0.2.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_windows-0.2.2/lib/
get_it
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/get_it-7.7.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/get_it-7.7.0/lib/
glob
2.19
file:///Users/<USER>/.pub-cache/hosted/pub.dev/glob-2.1.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/glob-2.1.2/lib/
graphs
2.18
file:///Users/<USER>/.pub-cache/hosted/pub.dev/graphs-2.3.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/graphs-2.3.1/lib/
hive
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/hive-2.2.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/hive-2.2.3/lib/
hive_flutter
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/hive_flutter-1.1.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/hive_flutter-1.1.0/lib/
hive_generator
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/hive_generator-2.0.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/hive_generator-2.0.1/lib/
html
2.19
file:///Users/<USER>/.pub-cache/hosted/pub.dev/html-0.15.4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/html-0.15.4/lib/
http
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/http-1.1.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/http-1.1.0/lib/
http_multi_server
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/http_multi_server-3.2.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/http_multi_server-3.2.1/lib/
http_parser
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.0.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.0.2/lib/
image
2.15
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image-4.1.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image-4.1.3/lib/
image_picker
2.19
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker-1.0.4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker-1.0.4/lib/
image_picker_android
2.19
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_android-0.8.8+2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_android-0.8.8+2/lib/
image_picker_for_web
2.19
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_for_web-3.0.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_for_web-3.0.1/lib/
image_picker_ios
2.19
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_ios-0.8.8+4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_ios-0.8.8+4/lib/
image_picker_linux
2.19
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_linux-0.2.1+1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_linux-0.2.1+1/lib/
image_picker_macos
2.19
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_macos-0.2.1+1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_macos-0.2.1+1/lib/
image_picker_platform_interface
2.19
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.9.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.9.1/lib/
image_picker_windows
2.19
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_windows-0.2.1+1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_windows-0.2.1+1/lib/
infinite_scroll_pagination
2.14
file:///Users/<USER>/.pub-cache/hosted/pub.dev/infinite_scroll_pagination-4.0.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/infinite_scroll_pagination-4.0.0/lib/
intl
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.19.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.19.0/lib/
io
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/io-1.0.4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/io-1.0.4/lib/
jovial_misc
2.14
file:///Users/<USER>/.pub-cache/hosted/pub.dev/jovial_misc-0.8.5/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/jovial_misc-0.8.5/lib/
jovial_svg
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/jovial_svg-1.1.19/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/jovial_svg-1.1.19/lib/
js
2.19
file:///Users/<USER>/.pub-cache/hosted/pub.dev/js-0.6.7/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/js-0.6.7/lib/
json_annotation
2.19
file:///Users/<USER>/.pub-cache/hosted/pub.dev/json_annotation-4.8.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/json_annotation-4.8.1/lib/
jwt_decoder
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/jwt_decoder-2.0.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/jwt_decoder-2.0.1/lib/
leak_tracker
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker-10.0.8/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker-10.0.8/lib/
leak_tracker_flutter_testing
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_flutter_testing-3.0.9/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_flutter_testing-3.0.9/lib/
leak_tracker_testing
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_testing-3.0.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_testing-3.0.1/lib/
logger
2.17
file:///Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.0.2+1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.0.2+1/lib/
logging
2.19
file:///Users/<USER>/.pub-cache/hosted/pub.dev/logging-1.2.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/logging-1.2.0/lib/
matcher
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/matcher-0.12.17/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/matcher-0.12.17/lib/
material_color_utilities
2.17
file:///Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/
material_design_icons_flutter
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/material_design_icons_flutter-7.0.7296/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/material_design_icons_flutter-7.0.7296/lib/
meta
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/meta-1.16.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/meta-1.16.0/lib/
mime
2.18
file:///Users/<USER>/.pub-cache/hosted/pub.dev/mime-1.0.4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/mime-1.0.4/lib/
mocktail
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/mocktail-1.0.4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/mocktail-1.0.4/lib/
nested
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/nested-1.0.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/nested-1.0.0/lib/
node_preamble
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/node_preamble-2.0.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/node_preamble-2.0.2/lib/
numerus
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/numerus-1.1.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/numerus-1.1.1/lib/
package_config
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/package_config-2.1.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/package_config-2.1.0/lib/
package_info_plus
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus-5.0.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus-5.0.1/lib/
package_info_plus_platform_interface
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus_platform_interface-2.0.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus_platform_interface-2.0.1/lib/
path
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/
path_parsing
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_parsing-1.0.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_parsing-1.0.1/lib/
path_provider
2.19
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider-2.1.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider-2.1.1/lib/
path_provider_android
2.19
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_android-2.2.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_android-2.2.1/lib/
path_provider_foundation
2.19
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.3.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.3.1/lib/
path_provider_linux
2.19
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/lib/
path_provider_platform_interface
2.19
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_platform_interface-2.1.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_platform_interface-2.1.1/lib/
path_provider_windows
2.19
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.2.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.2.1/lib/
petitparser
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.0.1/lib/
platform
2.19
file:///Users/<USER>/.pub-cache/hosted/pub.dev/platform-3.1.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/platform-3.1.3/lib/
plugin_platform_interface
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/plugin_platform_interface-2.1.7/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/plugin_platform_interface-2.1.7/lib/
pointycastle
2.14
file:///Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.7.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.7.3/lib/
pool
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/pool-1.5.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/pool-1.5.1/lib/
preload_page_view
2.19
file:///Users/<USER>/.pub-cache/hosted/pub.dev/preload_page_view-0.2.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/preload_page_view-0.2.0/lib/
provider
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/provider-6.1.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/provider-6.1.2/lib/
pub_semver
2.17
file:///Users/<USER>/.pub-cache/hosted/pub.dev/pub_semver-2.1.4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/pub_semver-2.1.4/lib/
pubspec_parse
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/pubspec_parse-1.3.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/pubspec_parse-1.3.0/lib/
qr_code_scanner
2.17
file:///Users/<USER>/.pub-cache/hosted/pub.dev/qr_code_scanner-1.0.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/qr_code_scanner-1.0.1/lib/
shelf
2.17
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shelf-1.4.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shelf-1.4.1/lib/
shelf_packages_handler
2.17
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shelf_packages_handler-3.0.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shelf_packages_handler-3.0.2/lib/
shelf_static
2.17
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shelf_static-1.1.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shelf_static-1.1.2/lib/
shelf_web_socket
2.17
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shelf_web_socket-1.0.4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/shelf_web_socket-1.0.4/lib/
sliver_tools
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/sliver_tools-0.2.12/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/sliver_tools-0.2.12/lib/
source_gen
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/source_gen-1.5.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/source_gen-1.5.0/lib/
source_helper
2.19
file:///Users/<USER>/.pub-cache/hosted/pub.dev/source_helper-1.3.4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/source_helper-1.3.4/lib/
source_map_stack_trace
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/source_map_stack_trace-2.1.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/source_map_stack_trace-2.1.1/lib/
source_maps
2.18
file:///Users/<USER>/.pub-cache/hosted/pub.dev/source_maps-0.10.12/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/source_maps-0.10.12/lib/
source_span
3.1
file:///Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/
sprintf
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/sprintf-7.0.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/sprintf-7.0.0/lib/
stack_trace
3.4
file:///Users/<USER>/.pub-cache/hosted/pub.dev/stack_trace-1.12.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/stack_trace-1.12.1/lib/
stream_channel
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/stream_channel-2.1.4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/stream_channel-2.1.4/lib/
stream_transform
2.14
file:///Users/<USER>/.pub-cache/hosted/pub.dev/stream_transform-2.1.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/stream_transform-2.1.0/lib/
string_scanner
3.1
file:///Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/lib/
term_glyph
3.1
file:///Users/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.2/lib/
test
3.5
file:///Users/<USER>/.pub-cache/hosted/pub.dev/test-1.25.15/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/test-1.25.15/lib/
test_api
3.5
file:///Users/<USER>/.pub-cache/hosted/pub.dev/test_api-0.7.4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/test_api-0.7.4/lib/
test_core
3.5
file:///Users/<USER>/.pub-cache/hosted/pub.dev/test_core-0.6.8/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/test_core-0.6.8/lib/
thingsboard_client
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/thingsboard_client-1.2.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/thingsboard_client-1.2.1/lib/
timeago
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.0/lib/
timezone
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/timezone-0.9.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/timezone-0.9.2/lib/
timing
2.14
file:///Users/<USER>/.pub-cache/hosted/pub.dev/timing-1.0.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/timing-1.0.1/lib/
typed_data
2.17
file:///Users/<USER>/.pub-cache/hosted/pub.dev/typed_data-1.3.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/typed_data-1.3.2/lib/
uni_links
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/uni_links-0.5.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/uni_links-0.5.1/lib/
uni_links_platform_interface
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/uni_links_platform_interface-1.0.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/uni_links_platform_interface-1.0.0/lib/
uni_links_web
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/uni_links_web-0.1.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/uni_links_web-0.1.0/lib/
universal_html
2.17
file:///Users/<USER>/.pub-cache/hosted/pub.dev/universal_html-2.2.4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/universal_html-2.2.4/lib/
universal_io
2.17
file:///Users/<USER>/.pub-cache/hosted/pub.dev/universal_io-2.2.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/universal_io-2.2.2/lib/
universal_platform
2.12
file:///Users/<USER>/.pub-cache/hosted/pub.dev/universal_platform-1.0.0+1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/universal_platform-1.0.0+1/lib/
url_launcher
3.1
file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher-6.2.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher-6.2.1/lib/
url_launcher_android
2.19
file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_android-6.2.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_android-6.2.0/lib/
url_launcher_ios
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_ios-6.2.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_ios-6.2.1/lib/
url_launcher_linux
2.19
file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_linux-3.1.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_linux-3.1.0/lib/
url_launcher_macos
2.19
file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_macos-3.1.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_macos-3.1.0/lib/
url_launcher_platform_interface
2.19
file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_platform_interface-2.2.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_platform_interface-2.2.0/lib/
url_launcher_web
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_web-2.2.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_web-2.2.1/lib/
url_launcher_windows
2.19
file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_windows-3.1.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_windows-3.1.0/lib/
uuid
2.14
file:///Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.2.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.2.1/lib/
vector_graphics
2.17
file:///Users/<USER>/.pub-cache/hosted/pub.dev/vector_graphics-1.1.9+1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/vector_graphics-1.1.9+1/lib/
vector_graphics_codec
2.17
file:///Users/<USER>/.pub-cache/hosted/pub.dev/vector_graphics_codec-1.1.9+1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/vector_graphics_codec-1.1.9+1/lib/
vector_graphics_compiler
2.19
file:///Users/<USER>/.pub-cache/hosted/pub.dev/vector_graphics_compiler-1.1.9+1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/vector_graphics_compiler-1.1.9+1/lib/
vector_math
2.14
file:///Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/
vm_service
3.3
file:///Users/<USER>/.pub-cache/hosted/pub.dev/vm_service-14.3.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/vm_service-14.3.1/lib/
watcher
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/watcher-1.1.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/watcher-1.1.0/lib/
web
3.2
file:///Users/<USER>/.pub-cache/hosted/pub.dev/web-0.3.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/web-0.3.0/lib/
web_socket_channel
2.15
file:///Users/<USER>/.pub-cache/hosted/pub.dev/web_socket_channel-2.4.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/web_socket_channel-2.4.0/lib/
webkit_inspection_protocol
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/webkit_inspection_protocol-1.2.1/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/webkit_inspection_protocol-1.2.1/lib/
win32
3.1
file:///Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.1.0/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.1.0/lib/
win32_registry
3.1
file:///Users/<USER>/.pub-cache/hosted/pub.dev/win32_registry-1.1.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/win32_registry-1.1.2/lib/
xdg_directories
2.19
file:///Users/<USER>/.pub-cache/hosted/pub.dev/xdg_directories-1.0.3/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/xdg_directories-1.0.3/lib/
xml
3.0
file:///Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.4.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.4.2/lib/
yaml
2.19
file:///Users/<USER>/.pub-cache/hosted/pub.dev/yaml-3.1.2/
file:///Users/<USER>/.pub-cache/hosted/pub.dev/yaml-3.1.2/lib/
sky_engine
3.7
file:///Users/<USER>/development/flutter/bin/cache/pkg/sky_engine/
file:///Users/<USER>/development/flutter/bin/cache/pkg/sky_engine/lib/
flutter
3.7
file:///Users/<USER>/development/flutter/packages/flutter/
file:///Users/<USER>/development/flutter/packages/flutter/lib/
flutter_localizations
3.7
file:///Users/<USER>/development/flutter/packages/flutter_localizations/
file:///Users/<USER>/development/flutter/packages/flutter_localizations/lib/
flutter_test
3.7
file:///Users/<USER>/development/flutter/packages/flutter_test/
file:///Users/<USER>/development/flutter/packages/flutter_test/lib/
flutter_web_plugins
3.7
file:///Users/<USER>/development/flutter/packages/flutter_web_plugins/
file:///Users/<USER>/development/flutter/packages/flutter_web_plugins/lib/
thingsboard_app
3.2
file:///Users/<USER>/projects/schnell_live_projects/sun-rider-crew/
file:///Users/<USER>/projects/schnell_live_projects/sun-rider-crew/lib/
2
