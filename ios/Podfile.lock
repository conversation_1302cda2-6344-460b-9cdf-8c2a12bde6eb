PODS:
  - device_info_plus (0.0.1):
    - Flutter
  - Firebase/CoreOnly (10.18.0):
    - FirebaseCore (= 10.18.0)
  - Firebase/Messaging (10.18.0):
    - Firebase/CoreOnly
    - FirebaseMessaging (~> 10.18.0)
  - firebase_core (2.24.2):
    - Firebase/CoreOnly (= 10.18.0)
    - Flutter
  - firebase_messaging (14.7.10):
    - Firebase/Messaging (= 10.18.0)
    - firebase_core
    - Flutter
  - FirebaseCore (10.18.0):
    - FirebaseCoreInternal (~> 10.0)
    - GoogleUtilities/Environment (~> 7.12)
    - GoogleUtilities/Logger (~> 7.12)
  - FirebaseCoreInternal (10.20.0):
    - "GoogleUtilities/NSData+zlib (~> 7.8)"
  - FirebaseInstallations (10.20.0):
    - FirebaseCore (~> 10.0)
    - GoogleUtilities/Environment (~> 7.8)
    - GoogleUtilities/UserDefaults (~> 7.8)
    - PromisesObjC (~> 2.1)
  - FirebaseMessaging (10.18.0):
    - FirebaseCore (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleDataTransport (~> 9.2)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.8)
    - GoogleUtilities/Environment (~> 7.8)
    - GoogleUtilities/Reachability (~> 7.8)
    - GoogleUtilities/UserDefaults (~> 7.8)
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - Flutter (1.0.0)
  - flutter_inappwebview (0.0.1):
    - Flutter
    - flutter_inappwebview/Core (= 0.0.1)
    - OrderedSet (~> 5.0)
  - flutter_inappwebview/Core (0.0.1):
    - Flutter
    - OrderedSet (~> 5.0)
  - flutter_local_notifications (0.0.1):
    - Flutter
  - flutter_secure_storage (6.0.0):
    - Flutter
  - geolocator_apple (1.2.0):
    - Flutter
  - GoogleDataTransport (9.3.0):
    - GoogleUtilities/Environment (~> 7.7)
    - nanopb (< 2.30910.0, >= 2.30908.0)
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleUtilities/AppDelegateSwizzler (7.12.0):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
  - GoogleUtilities/Environment (7.12.0):
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleUtilities/Logger (7.12.0):
    - GoogleUtilities/Environment
  - GoogleUtilities/Network (7.12.0):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (7.12.0)"
  - GoogleUtilities/Reachability (7.12.0):
    - GoogleUtilities/Logger
  - GoogleUtilities/UserDefaults (7.12.0):
    - GoogleUtilities/Logger
  - image_picker_ios (0.0.1):
    - Flutter
  - MTBBarcodeScanner (5.0.11)
  - nanopb (2.30909.1):
    - nanopb/decode (= 2.30909.1)
    - nanopb/encode (= 2.30909.1)
  - nanopb/decode (2.30909.1)
  - nanopb/encode (2.30909.1)
  - OrderedSet (5.0.0)
  - package_info_plus (0.4.5):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - PromisesObjC (2.3.1)
  - qr_code_scanner (0.2.0):
    - Flutter
    - MTBBarcodeScanner
  - url_launcher_ios (0.0.1):
    - Flutter

DEPENDENCIES:
  - device_info_plus (from `.symlinks/plugins/device_info_plus/ios`)
  - firebase_core (from `.symlinks/plugins/firebase_core/ios`)
  - firebase_messaging (from `.symlinks/plugins/firebase_messaging/ios`)
  - Flutter (from `Flutter`)
  - flutter_inappwebview (from `.symlinks/plugins/flutter_inappwebview/ios`)
  - flutter_local_notifications (from `.symlinks/plugins/flutter_local_notifications/ios`)
  - flutter_secure_storage (from `.symlinks/plugins/flutter_secure_storage/ios`)
  - geolocator_apple (from `.symlinks/plugins/geolocator_apple/ios`)
  - image_picker_ios (from `.symlinks/plugins/image_picker_ios/ios`)
  - package_info_plus (from `.symlinks/plugins/package_info_plus/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - qr_code_scanner (from `.symlinks/plugins/qr_code_scanner/ios`)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)

SPEC REPOS:
  trunk:
    - Firebase
    - FirebaseCore
    - FirebaseCoreInternal
    - FirebaseInstallations
    - FirebaseMessaging
    - GoogleDataTransport
    - GoogleUtilities
    - MTBBarcodeScanner
    - nanopb
    - OrderedSet
    - PromisesObjC

EXTERNAL SOURCES:
  device_info_plus:
    :path: ".symlinks/plugins/device_info_plus/ios"
  firebase_core:
    :path: ".symlinks/plugins/firebase_core/ios"
  firebase_messaging:
    :path: ".symlinks/plugins/firebase_messaging/ios"
  Flutter:
    :path: Flutter
  flutter_inappwebview:
    :path: ".symlinks/plugins/flutter_inappwebview/ios"
  flutter_local_notifications:
    :path: ".symlinks/plugins/flutter_local_notifications/ios"
  flutter_secure_storage:
    :path: ".symlinks/plugins/flutter_secure_storage/ios"
  geolocator_apple:
    :path: ".symlinks/plugins/geolocator_apple/ios"
  image_picker_ios:
    :path: ".symlinks/plugins/image_picker_ios/ios"
  package_info_plus:
    :path: ".symlinks/plugins/package_info_plus/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  qr_code_scanner:
    :path: ".symlinks/plugins/qr_code_scanner/ios"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"

SPEC CHECKSUMS:
  device_info_plus: c6fb39579d0f423935b0c9ce7ee2f44b71b9fce6
  Firebase: 414ad272f8d02dfbf12662a9d43f4bba9bec2a06
  firebase_core: 0af4a2b24f62071f9bf283691c0ee41556dcb3f5
  firebase_messaging: 90e8a6db84b6e1e876cebce4f30f01dc495e7014
  FirebaseCore: 2322423314d92f946219c8791674d2f3345b598f
  FirebaseCoreInternal: efeeb171ac02d623bdaefe121539939821e10811
  FirebaseInstallations: 558b1da7d65afeb996fd5c814332f013234ece4e
  FirebaseMessaging: 9bc34a98d2e0237e1b121915120d4d48ddcf301e
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  flutter_inappwebview: 3d32228f1304635e7c028b0d4252937730bbc6cf
  flutter_local_notifications: 0c0b1ae97e741e1521e4c1629a459d04b9aec743
  flutter_secure_storage: 23fc622d89d073675f2eaa109381aefbcf5a49be
  geolocator_apple: cc556e6844d508c95df1e87e3ea6fa4e58c50401
  GoogleDataTransport: 57c22343ab29bc686febbf7cbb13bad167c2d8fe
  GoogleUtilities: 0759d1a57ebb953965c2dfe0ba4c82e95ccc2e34
  image_picker_ios: 4a8aadfbb6dc30ad5141a2ce3832af9214a705b5
  MTBBarcodeScanner: f453b33c4b7dfe545d8c6484ed744d55671788cb
  nanopb: d4d75c12cd1316f4a64e3c6963f879ecd4b5e0d5
  OrderedSet: aaeb196f7fef5a9edf55d89760da9176ad40b93c
  package_info_plus: 115f4ad11e0698c8c1c5d8a689390df880f47e85
  path_provider_foundation: 29f094ae23ebbca9d3d0cec13889cd9060c0e943
  PromisesObjC: c50d2056b5253dadbd6c2bea79b0674bd5a52fa4
  qr_code_scanner: bb67d64904c3b9658ada8c402e8b4d406d5d796e
  url_launcher_ios: bf5ce03e0e2088bad9cc378ea97fa0ed5b49673b

PODFILE CHECKSUM: c4c93c5f6502fe2754f48404d3594bf779584011

COCOAPODS: 1.14.3
