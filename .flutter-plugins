# This is a generated file; do not edit or check into version control.
device_info_plus=/Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-9.1.1/
file_selector_linux=/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_linux-0.9.2+1/
file_selector_macos=/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_macos-0.9.3+3/
file_selector_windows=/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_windows-0.9.3+1/
firebase_core=/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core-2.24.2/
firebase_core_web=/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core_web-2.10.0/
firebase_messaging=/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_messaging-14.7.10/
firebase_messaging_web=/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_messaging_web-3.5.18/
flutter_app_badger=/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_app_badger-1.5.0/
flutter_inappwebview=/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview-5.8.0/
flutter_local_notifications=/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications-16.3.0/
flutter_plugin_android_lifecycle=/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_plugin_android_lifecycle-2.0.17/
flutter_secure_storage=/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-9.0.0/
flutter_secure_storage_linux=/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_linux-1.2.0/
flutter_secure_storage_macos=/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_macos-3.0.1/
flutter_secure_storage_web=/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_web-1.1.2/
flutter_secure_storage_windows=/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_windows-3.0.0/
geolocator=/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator-10.1.0/
geolocator_android=/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_android-4.4.0/
geolocator_apple=/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_apple-2.3.2/
geolocator_web=/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_web-2.2.0/
geolocator_windows=/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_windows-0.2.2/
image_picker=/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker-1.0.4/
image_picker_android=/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_android-0.8.8+2/
image_picker_for_web=/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_for_web-3.0.1/
image_picker_ios=/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_ios-0.8.8+4/
image_picker_linux=/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_linux-0.2.1+1/
image_picker_macos=/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_macos-0.2.1+1/
image_picker_windows=/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_windows-0.2.1+1/
package_info_plus=/Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus-5.0.1/
path_provider=/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider-2.1.1/
path_provider_android=/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_android-2.2.1/
path_provider_foundation=/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.3.1/
path_provider_linux=/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/
path_provider_windows=/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.2.1/
qr_code_scanner=/Users/<USER>/.pub-cache/hosted/pub.dev/qr_code_scanner-1.0.1/
uni_links=/Users/<USER>/.pub-cache/hosted/pub.dev/uni_links-0.5.1/
uni_links_web=/Users/<USER>/.pub-cache/hosted/pub.dev/uni_links_web-0.1.0/
url_launcher=/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher-6.2.1/
url_launcher_android=/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_android-6.2.0/
url_launcher_ios=/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_ios-6.2.1/
url_launcher_linux=/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_linux-3.1.0/
url_launcher_macos=/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_macos-3.1.0/
url_launcher_web=/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_web-2.2.1/
url_launcher_windows=/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_windows-3.1.0/
